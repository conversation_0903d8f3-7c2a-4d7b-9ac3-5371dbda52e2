// 修复后的 handleLevel 函数实现
void CMvTabWidgetExe::handleLevel(int level)
{
    QString str = this->property("allTabLevelInfo").toString();
    QStringList list = str.split("$-");
    int nSize = list.size();
    
    // 添加边界检查，确保不会越界
    for (int i = 0; i < nSize && i < this->count(); ++i)
    {
        int visibleLevel = 10;
        int enabledLevel = 10;
        QStringList levelList = list[i].split("-");
        if (levelList.size() == 2)
        {
            visibleLevel = levelList[0].toInt();
            enabledLevel = levelList[1].toInt();
        }
        
        // 使用setTabVisible来隐藏/显示整个标签页（包括标签头）
        bool shouldBeVisible = (level <= visibleLevel);
        this->setTabVisible(i, shouldBeVisible);
        
        // 只有当标签页可见时才设置启用状态
        if (shouldBeVisible)
        {
            bool shouldBeEnabled = (level <= enabledLevel);
            this->setTabEnabled(i, shouldBeEnabled);
        }
    }
}

/*
主要修改说明：

1. 使用 setTabVisible(i, shouldBeVisible) 替代 widget(i)->setVisible()
   - setTabVisible() 会隐藏整个标签页，包括标签头
   - widget()->setVisible() 只隐藏标签页内容，标签头仍然可见

2. 使用 setTabEnabled(i, shouldBeEnabled) 替代 widget(i)->setEnabled()
   - 这是更合适的API来控制标签页的启用状态

3. 添加边界检查 i < this->count()
   - 防止访问不存在的标签页索引

4. 逻辑优化
   - 只有当标签页可见时才设置启用状态
   - 避免对隐藏的标签页进行不必要的操作

使用示例：
- 如果 allTabLevelInfo = "5-3$-8-6$-2-1"
- 当 level = 4 时：
  - 第0个标签页：visibleLevel=5, enabledLevel=3 → 可见但禁用
  - 第1个标签页：visibleLevel=8, enabledLevel=6 → 可见且启用  
  - 第2个标签页：visibleLevel=2, enabledLevel=1 → 隐藏
*/
